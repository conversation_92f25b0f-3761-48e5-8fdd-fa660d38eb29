# Legacy Visa Board Card Patterns

## Component Overview

- **Component Name**: Visa Board Card (Legacy)
- **File Path**: ~~`visa_board.jsx`~~ (Deleted - August 2024)
- **Purpose**: Simplified card component originally designed for visa board interfaces
- **Complexity Level**: Low
- **Status**: **DEPRECATED** - Functionality migrated to modern components
- **Modern Equivalent**: `VisaPreview.tsx` component

## Historical Context

### Original Implementation
The legacy Visa Board Card was a standalone, simplified card component that existed outside the main design system. It was originally created for specific visa board interface requirements but has since been superseded by more comprehensive solutions.

### Migration Timeline
- **Pre-2024**: Original `visa_board.jsx` implementation
- **August 2024**: File deleted and functionality migrated
- **Current**: Functionality available through `VisaPreview.tsx` and design system cards

## Legacy Component Structure

### Original Interface (Reconstructed)
```typescript
interface VisaBoardCardProps {
  title?: string
  extra?: React.ReactNode
  children: React.ReactNode
  className?: string
}
```

### Legacy Implementation Pattern
```typescript
// Simplified version for historical reference
const VisaBoardCard = ({ title, extra, children, className = "" }) => (
  <div className={`rounded-2xl shadow-sm border border-gray-200 bg-white ${className}`}>
    {(title || extra) && (
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-100">
        <div className="font-medium text-gray-800">{title}</div>
        <div>{extra}</div>
      </div>
    )}
    <div className="p-4">{children}</div>
  </div>
)
```

## Legacy Design Specifications

### Visual Characteristics
- **Border Radius**: `rounded-2xl` (24px) - Non-standard compared to design system
- **Shadow**: `shadow-sm` - Minimal elevation
- **Border**: `border-gray-200` - Hardcoded gray instead of design tokens
- **Background**: `bg-white` - Hardcoded white instead of theme-aware tokens
- **Typography**: `font-medium text-gray-800` - Hardcoded colors

### Layout Structure
- **Header Section**: Optional title and extra content area
- **Content Section**: Main content area with padding
- **Spacing**: `px-4 py-2` (header), `p-4` (content)

### Limitations
- **No Design System Integration**: Used hardcoded colors and spacing
- **No Theme Support**: No light/dark mode compatibility
- **Limited Accessibility**: Basic semantic structure only
- **No Interactive States**: No hover, focus, or active state handling

## Migration to Modern Components

### Primary Replacement: VisaPreview Component

The legacy Visa Board Card functionality has been fully migrated to the `VisaPreview.tsx` component, which provides:

#### Enhanced Features
- **Full Design System Integration**: Uses design tokens and theme support
- **Advanced Chart Integration**: Multiple chart types and data visualization
- **Health Indicators**: NHI/THI health monitoring
- **Responsive Design**: Container queries and responsive behavior
- **Accessibility**: Full ARIA support and keyboard navigation
- **Interactive States**: Comprehensive hover, focus, and active states

#### Modern Implementation
```typescript
// Modern equivalent using VisaPreview
import VisaPreview from "@/components/shared/VisaPreview"

function ModernVisaCard() {
  return (
    <VisaPreview
      className="custom-styling"
      hideHeader={false}
      hideDataControls={true}
      timeRange="15m"
      isSimulatedData={false}
    />
  )
}
```

### Alternative: Basic UI Card System

For simple card needs, use the standardized Basic UI Card System:

```typescript
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"

function SimpleCard() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Modern Card Title</CardTitle>
      </CardHeader>
      <CardContent>
        <p>Content using design system tokens</p>
      </CardContent>
    </Card>
  )
}
```

## Migration Guide

### Step 1: Identify Usage Patterns
Before migrating, identify how the legacy card was being used:

- **Simple Content Display**: Use Basic UI Card System
- **Data Visualization**: Use VisaPreview component
- **Monitor Displays**: Use MonitorCard component

### Step 2: Replace Implementation
```typescript
// ❌ Legacy Pattern
<VisaBoardCard title="Service Status">
  <div>Service is running</div>
</VisaBoardCard>

// ✅ Modern Pattern
<Card>
  <CardHeader>
    <CardTitle>Service Status</CardTitle>
  </CardHeader>
  <CardContent>
    <div>Service is running</div>
  </CardContent>
</Card>
```

### Step 3: Update Styling
```css
/* ❌ Legacy Styling */
.legacy-card {
  border-radius: 24px;
  background: white;
  border: 1px solid #e5e7eb;
}

/* ✅ Modern Styling */
.modern-card {
  border-radius: var(--corner-sm);
  background: var(--card);
  border: 1px solid var(--border);
}
```

### Step 4: Enhance Accessibility
```typescript
// ✅ Modern Accessible Pattern
<Card role="region" aria-labelledby="card-title">
  <CardHeader>
    <CardTitle id="card-title">Accessible Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Content with proper semantic structure</p>
  </CardContent>
</Card>
```

## Comparison Table

| Feature | Legacy Visa Board Card | Modern VisaPreview | Basic UI Card |
|---------|----------------------|-------------------|---------------|
| **Design System** | ❌ Hardcoded values | ✅ Full integration | ✅ Full integration |
| **Theme Support** | ❌ Light only | ✅ Light/Dark | ✅ Light/Dark |
| **Accessibility** | ⚠️ Basic | ✅ Comprehensive | ✅ Comprehensive |
| **Charts** | ❌ None | ✅ Advanced | ❌ None |
| **Health Indicators** | ❌ None | ✅ NHI/THI | ❌ None |
| **Interactive States** | ❌ None | ✅ Full support | ✅ Full support |
| **Responsive** | ⚠️ Basic | ✅ Advanced | ✅ Advanced |
| **Bundle Size** | ✅ Small | ⚠️ Large | ✅ Small |
| **Complexity** | ✅ Low | ⚠️ High | ✅ Medium |

## Best Practices for Migration

### Do's
- ✅ Use design tokens instead of hardcoded values
- ✅ Implement proper accessibility features
- ✅ Follow established component patterns
- ✅ Test across light/dark themes
- ✅ Ensure responsive behavior

### Don'ts
- ❌ Recreate legacy hardcoded styling
- ❌ Skip accessibility improvements
- ❌ Use non-standard border radius values
- ❌ Ignore design system conventions
- ❌ Implement without proper testing

## Legacy Code Archive

For historical reference, the legacy implementation pattern:

```typescript
// Historical implementation (DO NOT USE)
const LegacyVisaBoardCard = ({ title, extra, children, className = "" }) => (
  <div className={`rounded-2xl shadow-sm border border-gray-200 bg-white ${className}`}>
    {(title || extra) && (
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-100">
        <div className="font-medium text-gray-800">{title}</div>
        <div>{extra}</div>
      </div>
    )}
    <div className="p-4">{children}</div>
  </div>
)
```

## Conclusion

The legacy Visa Board Card served its purpose but has been successfully replaced by more robust, accessible, and maintainable solutions. All new development should use the modern component alternatives that provide better user experience, accessibility, and maintainability.

---

**Related Components**: [VisaPreview](./visa-preview-system.md) | [Basic UI Card](./basic-ui-card-system.md) | [MonitorCard](./monitor-card-system.md)
